<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategy Builder - Professional Trading Analysis</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/multi-panel.css">
    <link rel="stylesheet" href="/static/css/multi-indicator-config.css">
    <script src="https://unpkg.com/lightweight-charts@4.2.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        /* Professional Trading Interface Styles */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Trebuchet MS', Arial, sans-serif;
            background: #131722;
            color: #d1d4dc;
            overflow-x: hidden;
        }

        .trading-interface {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Professional Header */
        .professional-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            background: linear-gradient(135deg, #1e222d 0%, #2a2e39 100%);
            border-bottom: 1px solid #363c4e;
            min-height: 50px;
        }

        .brand-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .brand-name {
            font-size: 18px;
            font-weight: 700;
            color: #2962ff;
        }

        .symbol-display {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .current-symbol {
            font-size: 16px;
            font-weight: 700;
            color: #fff;
        }

        .current-price {
            font-size: 16px;
            font-weight: 600;
            color: #26a69a;
        }

        .price-change {
            font-size: 14px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
        }

        .price-change.positive {
            color: #26a69a;
            background: rgba(38, 166, 154, 0.1);
        }

        .price-change.negative {
            color: #ef5350;
            background: rgba(239, 83, 80, 0.1);
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .connection-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef5350;
        }

        .status-dot.connected {
            background: #26a69a;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Professional Chart Container */
        .professional-chart-container {
            flex: 1;
            position: relative;
            background: #131722;
            min-height: 500px;
        }

        #professional-tradingview-chart {
            width: 100%;
            height: 100%;
        }

        /* Left Sidebar Layout */
        .app-container {
            display: flex;
            height: 100vh;
        }

        .left-sidebar {
            width: 350px;
            background: #1e222d;
            border-right: 1px solid #363c4e;
            overflow-y: auto;
            flex-shrink: 0;
        }

        .sidebar-tabs {
            display: flex;
            border-bottom: 1px solid #363c4e;
        }

        .sidebar-tab {
            flex: 1;
            padding: 10px 12px;
            background: #2a2e39;
            color: #d1d4dc;
            border: none;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
            border-right: 1px solid #363c4e;
        }

        .sidebar-tab:last-child {
            border-right: none;
        }

        .sidebar-tab.active {
            background: #1e222d;
            color: #2962ff;
        }

        .sidebar-tab:hover:not(.active) {
            background: #363c4e;
        }

        .sidebar-content {
            padding: 20px;
        }

        .sidebar-panel {
            display: none;
        }

        .sidebar-panel.active {
            display: block;
        }

        .main-content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Trading Interface Layout */
        .trading-interface {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .professional-header {
            flex-shrink: 0;
        }

        .professional-chart-container {
            flex: 1;
            overflow: hidden;
        }

        /* Sidebar Sections */
        .sidebar-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #363c4e;
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .sidebar-section h4 {
            margin: 0 0 15px 0;
            color: #d1d4dc;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .control-group {
            margin-bottom: 12px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #b2b5be;
            font-size: 12px;
            font-weight: 600;
        }

        .control-group input,
        .control-group select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #444;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 13px;
        }

        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #2962ff;
            box-shadow: 0 0 0 2px rgba(41, 98, 255, 0.2);
        }

        .indicator-group {
            margin-bottom: 15px;
            padding: 12px;
            background: #2a2e39;
            border-radius: 6px;
            border: 1px solid #363c4e;
        }

        .indicator-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            color: #d1d4dc;
            font-weight: 600;
        }

        .indicator-group input[type="checkbox"] {
            width: auto;
        }

        .indicator-group input[type="number"] {
            margin-top: 5px;
        }

        .indicator-params {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-top: 8px;
        }

        .indicator-params input {
            font-size: 12px;
            padding: 6px 8px;
        }

        .param-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
        }

        .param-row input[type="number"] {
            flex: 1;
            min-width: 80px;
        }

        .param-row input[type="color"] {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            padding: 0;
        }

        .param-row input[type="range"] {
            flex: 1;
            margin: 0 8px;
        }

        .small-label {
            font-size: 11px;
            color: #b0b3b8;
            min-width: 60px;
        }

        #opacity-value {
            font-size: 11px;
            color: #26a69a;
            min-width: 30px;
        }

        .data-info {
            background: #2a2f3a;
            border: 1px solid #4a5568;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 4px;
        }

        #data-count-display {
            font-size: 12px;
            color: #26a69a;
            font-weight: 600;
        }

        .status-message {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-message.success {
            background: rgba(38, 166, 154, 0.1);
            border: 1px solid #26a69a;
            color: #26a69a;
        }

        .status-message.error {
            background: rgba(239, 83, 80, 0.1);
            border: 1px solid #ef5350;
            color: #ef5350;
        }

        .status-message.loading {
            background: rgba(41, 98, 255, 0.1);
            border: 1px solid #2962ff;
            color: #2962ff;
        }

        /* Indicator Tooltip Styles */
        .indicator-tooltip-popup {
            position: absolute;
            background: rgba(42, 46, 57, 0.95);
            border: 1px solid #363c4e;
            border-radius: 6px;
            padding: 12px;
            font-size: 12px;
            color: #d1d4dc;
            z-index: 1000;
            max-width: 250px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            display: none;
        }

        .indicator-tooltip-popup .tooltip-section {
            margin-bottom: 8px;
        }

        .indicator-tooltip-popup .tooltip-section:last-child {
            margin-bottom: 0;
        }

        .indicator-tooltip-popup strong {
            color: #26a69a;
            font-weight: 600;
        }

        .indicator-tooltip-popup br {
            line-height: 1.4;
        }



        .sr-controls input[type="range"] {
            flex: 1;
            margin: 0 5px;
        }

        .range-display {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            color: #888;
            margin-top: 2px;
        }

        .range-display span:nth-child(2) {
            color: #2962ff;
            font-weight: bold;
        }



        .sr-status.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .sr-status.warning {
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid rgba(255, 152, 0, 0.3);
            color: #ff9800;
        }

        /* Marking Tools Styles */
        .marking-sidebar {
            background: linear-gradient(135deg, #2a1810 0%, #1e1e1e 100%);
        }

        .marks-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #444;
            border-radius: 4px;
            background: #222;
            font-size: 11px;
        }

        .no-marks {
            padding: 12px;
            text-align: center;
            color: #888;
            font-style: italic;
            font-size: 11px;
        }

        .trade-pair {
            padding: 6px 8px;
            border-bottom: 1px solid #333;
            font-size: 10px;
        }

        .trade-pair:last-child {
            border-bottom: none;
        }

        .trade-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            font-weight: bold;
        }

        .trade-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            font-size: 9px;
            color: #ccc;
        }

        .entry-info, .exit-info {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .mark-side {
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .mark-side.buy {
            background: #4caf50;
            color: white;
        }

        .mark-side.sell {
            background: #f44336;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            background: rgba(255, 107, 53, 0.1);
            border-radius: 4px;
            border: 1px solid rgba(255, 107, 53, 0.3);
        }

        .stat-label {
            font-size: 10px;
            color: #888;
            margin-bottom: 2px;
        }

        .stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #ff6b35;
        }

        /* Modal Styles */
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #444;
            background: #2a2a2a;
        }

        .modal-header h3 {
            margin: 0;
            color: #fff;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-info {
            background: rgba(41, 98, 255, 0.1);
            border: 1px solid rgba(41, 98, 255, 0.3);
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: bold;
            color: #2962ff;
        }

        .info-value {
            color: #fff;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #ddd;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #444;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 13px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 60px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 15px 20px;
            border-top: 1px solid #444;
            background: #2a2a2a;
        }

        /* Modal Data Sections */
        .market-data-section,
        .ohlcv-section,
        .indicators-section,
        .pnl-section,
        .entry-form-section,
        .exit-form-section {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(41, 98, 255, 0.05);
            border: 1px solid rgba(41, 98, 255, 0.2);
            border-radius: 6px;
        }

        .market-data-section h4,
        .ohlcv-section h4,
        .indicators-section h4,
        .pnl-section h4,
        .entry-form-section h4,
        .exit-form-section h4 {
            margin: 0 0 10px 0;
            color: #2962ff;
            font-size: 14px;
            font-weight: bold;
        }

        .data-grid,
        .ohlcv-grid,
        .pnl-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .data-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }

        .data-label {
            font-weight: bold;
            color: #888;
            font-size: 12px;
        }

        .data-value {
            color: #fff;
            font-size: 12px;
            font-weight: 500;
        }

        .indicators-container {
            max-height: 200px;
            overflow-y: auto;
        }

        .indicator-group {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .indicator-group h5 {
            margin: 0 0 8px 0;
            color: #ff6b35;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .indicator-values {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
        }

        .indicator-item {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }

        .indicator-label {
            color: #aaa;
        }

        .indicator-value {
            color: #fff;
            font-weight: 500;
        }

        .pnl-positive {
            color: #4caf50 !important;
        }

        .pnl-negative {
            color: #f44336 !important;
        }

        .pnl-neutral {
            color: #888 !important;
        }

        /* Hover Details Tooltip */
        .hover-details {
            position: relative;
            cursor: help;
            border-bottom: 1px dotted #888;
        }

        .hover-details:hover .tooltip {
            display: block;
        }

        .tooltip {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 10px 12px;
            border-radius: 6px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 1000;
            border: 1px solid #444;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            margin-bottom: 5px;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.95) transparent transparent transparent;
        }

        .tooltip-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .tooltip-row {
            display: flex;
            justify-content: space-between;
            gap: 15px;
        }

        .tooltip-label {
            color: #aaa;
            font-weight: bold;
        }

        .tooltip-value {
            color: #fff;
        }

        /* Hover Details Tooltip */
        .hover-details {
            position: relative;
            cursor: help;
        }

        .hover-details:hover .tooltip {
            display: block;
        }

        .tooltip {
            display: none;
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 1000;
            border: 1px solid #444;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
        }

        .data-value.hover-details {
            border-bottom: 1px dotted #888;
        }

        /* Theme Toggle Styles */
        .theme-controls {
            padding: 10px 0;
        }

        .theme-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #363c4e;
            transition: 0.3s;
            border-radius: 24px;
            border: 1px solid #4a5568;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: #d1d4dc;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #26a69a;
            border-color: #26a69a;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
            background-color: white;
        }

        .theme-label {
            font-size: 14px;
            font-weight: 600;
            color: #d1d4dc;
        }

        .theme-info {
            color: #b0b3b8;
            font-size: 11px;
            line-height: 1.3;
        }

        .trade-controls,
        .export-controls,
        .sr-controls {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .trade-controls select {
            margin: 5px 0;
        }

        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
            text-align: center;
        }

        .btn-primary {
            background: #2962ff;
            color: white;
        }

        .btn-primary:hover {
            background: #1e88e5;
        }

        .btn-secondary {
            background: #444;
            color: #b2b5be;
        }

        .btn-secondary:hover {
            background: #555;
            color: #fff;
        }

        .btn-success {
            background: #26a69a;
            color: white;
        }

        .btn-success:hover {
            background: #00897b;
        }

        .btn-danger {
            background: #ef5350;
            color: white;
        }

        .btn-danger:hover {
            background: #e53935;
        }

        .btn-info {
            background: #29b6f6;
            color: white;
        }

        .btn-info:hover {
            background: #0288d1;
        }

        .status.error {
            color: #ef5350;
        }

        .status.info {
            color: #26a69a;
        }

        /* Fetch Result Styling */
        .fetch-result {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-top: 10px;
        }

        .fetch-result.success {
            background: rgba(38, 166, 154, 0.1);
            border: 1px solid #26a69a;
            color: #26a69a;
        }

        .fetch-result.error {
            background: rgba(239, 83, 80, 0.1);
            border: 1px solid #ef5350;
            color: #ef5350;
        }

        .fetch-result.info {
            background: rgba(41, 98, 255, 0.1);
            border: 1px solid #2962ff;
            color: #2962ff;
        }

        /* Drag and Drop Styles */
        .mark-tooltip {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            max-width: 200px;
        }

        .mark-context-menu {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 13px;
        }

        .context-menu-item {
            transition: background-color 0.2s ease;
        }

        .context-menu-item:hover {
            background-color: #363c4e !important;
        }

        /* Dragging cursor styles */
        .chart-container.dragging {
            cursor: grabbing !important;
        }

        .chart-container.dragging * {
            cursor: grabbing !important;
        }

        /* Mark hover effects */
        .chart-container:not(.marking-mode) {
            cursor: default;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Left Sidebar -->
        <div class="left-sidebar">
            <!-- Sidebar Tabs -->
            <div class="sidebar-tabs">
                <button class="sidebar-tab active" data-tab="strategy">Strategy</button>
                <button class="sidebar-tab" data-tab="indicators">Indicators</button>
                <button class="sidebar-tab" data-tab="marking">Marking</button>
            </div>

            <!-- Strategy Tools Panel -->
            <div class="sidebar-panel active" id="strategy-panel">
                <div class="sidebar-content">
                    <h3 style="margin-top: 0; color: #2962ff;">Strategy Builder</h3>

                    <!-- Strategy Management -->
                    <div class="sidebar-section">
                        <h4>Strategy Management</h4>
                        <div class="control-group">
                            <label for="strategy-select">Current Strategy:</label>
                            <select id="strategy-select">
                                <option value="">Select Strategy...</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <input type="text" id="new-strategy-name" placeholder="New strategy name..." style="margin-bottom: 8px;">
                            <button id="create-strategy" class="btn btn-primary" style="width: 100%;">Create New Strategy</button>
                        </div>
                    </div>

                    <!-- Data Controls -->
                    <div class="sidebar-section">
                        <h4>Data Controls</h4>
                        <div class="control-group">
                            <label for="exchange">Exchange:</label>
                            <select id="exchange">
                                <option value="binance">Binance</option>
                                <option value="mexc">MEXC</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label for="symbol">Symbol:</label>
                            <input type="text" id="symbol" placeholder="BTCUSDT" value="BTCUSDT">
                        </div>

                        <div class="control-group">
                            <label for="timeframe">Timeframe:</label>
                            <select id="timeframe">
                                <option value="1m">1 Minute</option>
                                <option value="3m">3 Minutes</option>
                                <option value="5m">5 Minutes</option>
                                <option value="15m" selected>15 Minutes</option>
                                <option value="30m">30 Minutes</option>
                                <option value="1h">1 Hour</option>
                                <option value="2h">2 Hours</option>
                                <option value="4h">4 Hours</option>
                                <option value="1d">1 Day</option>
                                <option value="1w">1 Week</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>Data Range:</label>
                            <div class="data-info">
                                <span id="data-count-display">Select date range to fetch data</span>
                            </div>
                        </div>

                        <div class="control-group">
                            <label for="start-date">Start Date:</label>
                            <input type="datetime-local" id="start-date" style="width: 100%;">
                        </div>

                        <div class="control-group">
                            <label for="end-date">End Date:</label>
                            <input type="datetime-local" id="end-date" style="width: 100%;">
                        </div>

                        <div class="control-group">
                            <button id="fetchData" class="btn btn-primary">Fetch Data</button>
                            <button id="loadData" class="btn btn-secondary" disabled>Load from DB</button>
                            <button id="testChart" class="btn btn-info">Test Chart</button>
                        </div>

                        <div id="fetch-result" style="margin-top: 10px; padding: 8px; border-radius: 4px; font-size: 12px; display: none;"></div>
                    </div>



                    <!-- Chart Theme Controls -->
                    <div class="sidebar-section">
                        <h4>Chart Theme</h4>
                        <div class="theme-controls">
                            <div class="theme-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="theme-toggle" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="theme-label" id="theme-label">Dark Theme</span>
                            </div>
                            <div class="theme-info">
                                <small>Toggle between light and dark chart backgrounds</small>
                            </div>
                        </div>
                    </div>



                    <!-- Data Export -->
                    <div class="sidebar-section">
                        <h4>Data Export</h4>
                        <div class="export-controls">
                            <button id="exportMarks" class="btn btn-secondary">Export Marks</button>
                            <button id="exportTrades" class="btn btn-secondary">Export Trades</button>
                            <button id="viewStrategyLog" class="btn btn-info">View Strategy Log</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Multi-Indicator Panel -->
            <div class="sidebar-panel" id="indicators-panel">
                <div class="sidebar-content">
                    <div class="indicator-config-header">
                        <h3 style="margin-top: 0; color: #4CAF50;">Technical Indicators</h3>
                        <div class="indicator-config-controls">
                            <button id="save-indicator-config" class="btn-save-config">Save Configuration</button>
                            <button id="reset-indicator-config" class="btn-reset-config">Reset All</button>
                        </div>
                    </div>

                    <!-- Strategy Selection Status -->
                    <div id="strategy-status" class="strategy-status">
                        <div class="status-message" id="strategy-status-message">
                            <span class="status-icon">⚠️</span>
                            <span class="status-text">Please select a strategy first</span>
                        </div>
                    </div>

                    <!-- Add Indicator Section -->
                    <div id="add-indicator-section" class="add-indicator-section" style="display: none;">
                        <h4>Add New Indicator</h4>
                        <div class="add-indicator-controls">
                            <select id="available-indicators" class="indicator-select">
                                <option value="">Select an indicator to add...</option>
                            </select>
                            <button id="add-indicator-btn" class="btn-add-indicator" disabled>Add Indicator</button>
                        </div>
                    </div>

                    <!-- Multi-Indicator Configuration Container -->
                    <div id="indicator-config-container">
                        <!-- Indicator panels will be dynamically generated here -->
                    </div>
                </div>
            </div>

            <!-- Marking Tools Panel -->
            <div class="sidebar-panel" id="marking-panel">
                <div class="sidebar-content">
                    <h3 style="margin-top: 0; color: #ff6b35;">Marking Tools</h3>

                    <!-- Active Marks Display -->
                    <div class="sidebar-section">
                        <h4>Active Marks</h4>
                        <div id="active-marks-list" class="marks-list">
                            <div class="no-marks">No active marks</div>
                        </div>
                    </div>

                    <!-- Mark Statistics -->
                    <div class="sidebar-section">
                        <h4>Statistics</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">Total Entries:</span>
                                <span class="stat-value" id="total-entries">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Open Positions:</span>
                                <span class="stat-value" id="open-positions">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Closed Trades:</span>
                                <span class="stat-value" id="closed-trades">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Win Rate:</span>
                                <span class="stat-value" id="win-rate">0%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Export Options -->
                    <div class="sidebar-section">
                        <h4>Export</h4>
                        <div class="export-controls">
                            <button id="export-marks" class="btn btn-secondary">Export Marks</button>
                            <button id="clear-all-marks" class="btn btn-danger">Clear All</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content-area">
            <div class="trading-interface">
                <!-- Professional Header -->
                <div class="professional-header">
                    <div class="brand-info">
                        <div class="brand-name">Strategy Builder</div>
                        <div class="symbol-display">
                            <div class="current-symbol" id="header-symbol">BTCUSDT</div>
                            <div class="current-price" id="header-price">$0.00</div>
                            <div class="price-change positive" id="header-change">+0.00%</div>
                        </div>
                    </div>

                    <div class="header-controls">
                        <div class="symbol-selector">
                            <input type="text" class="symbol-input" id="main-symbol-input" placeholder="Symbol" value="BTCUSDT">
                            <button class="btn btn-primary" id="main-change-symbol">Change</button>
                        </div>

                        <div class="timeframe-buttons">
                            <button class="timeframe-btn" data-interval="1m">1m</button>
                            <button class="timeframe-btn" data-interval="5m">5m</button>
                            <button class="timeframe-btn active" data-interval="15m">15m</button>
                            <button class="timeframe-btn" data-interval="1h">1h</button>
                            <button class="timeframe-btn" data-interval="4h">4h</button>
                            <button class="timeframe-btn" data-interval="1d">1d</button>
                        </div>

                        <div class="connection-indicator">
                            <div class="status-dot" id="main-connection-dot"></div>
                            <span id="main-connection-text">Connecting...</span>
                        </div>
                    </div>
                </div>

                <!-- Chart Container -->
                <div class="main-content">
                    <div class="professional-chart-container">
                        <div id="professional-tradingview-chart"></div>
                    </div>
                </div>

                <!-- Status Bar -->
                <div class="status-bar" style="display: flex; justify-content: space-between; align-items: center; padding: 6px 16px; background: #1e222d; border-top: 1px solid #363c4e; font-size: 12px; min-height: 32px;">
                    <div style="display: flex; align-items: center; gap: 20px;">
                        <div id="chart-status" class="status info">Initializing professional chart...</div>
                        <div id="crosshair-info" style="font-family: 'Courier New', monospace; color: #b2b5be;"></div>
                    </div>
                    <div>
                        <span>Powered by Binance API & TradingView Charts</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modal-body"></div>
        </div>
    </div>

    <!-- Unified Trade Modal with Type Selection -->
    <div id="trade-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add Trade Mark</h3>
                <span class="close" id="trade-modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Trade Type Selection -->
                <div class="trade-type-section">
                    <h4>Trade Type</h4>
                    <div class="form-group">
                        <label for="trade-type">Select Trade Type:</label>
                        <select id="trade-type">
                            <option value="entry">Entry - Open New Position</option>
                            <option value="exit">Exit - Close Existing Position</option>
                        </select>
                    </div>
                </div>

                <!-- Market Data Section -->
                <div class="market-data-section">
                    <h4>Market Data</h4>
                    <div class="data-grid">
                        <div class="data-row">
                            <span class="data-label">Symbol:</span>
                            <span class="data-value" id="trade-symbol"></span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">Timeframe:</span>
                            <span class="data-value" id="trade-timeframe"></span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">Time:</span>
                            <span class="data-value" id="trade-time"></span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">Price:</span>
                            <span class="data-value" id="trade-price"></span>
                        </div>
                    </div>
                </div>

                <!-- OHLCV Data Section -->
                <div class="ohlcv-section">
                    <h4>OHLCV Data</h4>
                    <div class="ohlcv-grid" id="trade-ohlcv">
                        <!-- OHLCV data will be populated here -->
                    </div>
                </div>

                <!-- Indicators Section -->
                <div class="indicators-section">
                    <h4>Technical Indicators</h4>
                    <div class="indicators-container" id="trade-indicators">
                        <!-- Indicators data will be populated here -->
                    </div>
                </div>

                <!-- Entry Form Section -->
                <div class="entry-form-section" id="trade-entry-form">
                    <h4>Entry Details</h4>
                    <div class="form-group">
                        <label for="trade-side">Entry Side:</label>
                        <select id="trade-side">
                            <option value="buy">Buy (Long)</option>
                            <option value="sell">Sell (Short)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="trade-quantity">Quantity:</label>
                        <input type="number" id="trade-quantity" step="0.01" min="0" placeholder="Enter quantity">
                    </div>

                    <div class="form-group">
                        <label for="trade-notes">Notes:</label>
                        <textarea id="trade-notes" rows="3" placeholder="Optional notes..."></textarea>
                    </div>
                </div>

                <!-- Exit Form Section -->
                <div class="exit-form-section" id="trade-exit-form" style="display: none;">
                    <h4>Exit Details</h4>
                    <div class="form-group">
                        <label for="trade-entry-select">Select Entry to Exit:</label>
                        <select id="trade-entry-select">
                            <option value="">Select an open entry...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="trade-exit-quantity">Exit Quantity:</label>
                        <input type="number" id="trade-exit-quantity" step="0.01" min="0" placeholder="Quantity to exit">
                    </div>
                </div>

                <!-- Profit/Loss Calculation (Exit only) -->
                <div class="pnl-section" id="trade-pnl-section" style="display: none;">
                    <h4>Profit/Loss Analysis</h4>
                    <div class="pnl-grid" id="trade-pnl">
                        <!-- P&L data will be populated here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="trade-cancel" class="btn btn-secondary">Cancel</button>
                <button id="trade-confirm" class="btn btn-primary" id="trade-submit-btn">Add Entry</button>
            </div>
        </div>
    </div>


    <!-- Scripts -->
    <script src="/static/js/tradingview-chart.js"></script>
    <script src="/static/js/theme-manager.js"></script>
    <script src="/static/js/strategy-manager.js"></script>
    <script src="/static/js/indicators.js"></script>
    <script src="/static/js/trades.js"></script>
    <script src="/static/js/marking-tools.js"></script>
    <script src="/static/js/multi-indicator-config.js"></script>
    <script src="/static/js/advanced-indicator-plotter.js"></script>
    <script>
        // Professional TradingView Chart Integration for Strategy Builder
        let professionalChart = null;

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize professional chart
            const initProfessionalChart = () => {
                const container = document.getElementById('professional-tradingview-chart');
                if (!container) {
                    console.log('Chart container not ready, retrying...');
                    setTimeout(initProfessionalChart, 100);
                    return;
                }

                if (typeof LightweightCharts !== 'undefined') {
                    // Get current theme from theme manager
                    const currentTheme = window.chartThemeManager ? window.chartThemeManager.getCurrentTheme().name : 'dark';

                    professionalChart = new TradingViewChart('professional-tradingview-chart', {
                        symbol: 'BTCUSDT',
                        interval: '15m',
                        theme: currentTheme,
                        enableWebSocket: false,
                        enableInfiniteHistory: false
                    });

                    window.professionalChart = professionalChart;

                    // Apply theme after chart is created
                    if (window.chartThemeManager) {
                        setTimeout(() => {
                            window.chartThemeManager.initializeChartTheme();
                        }, 100);
                    }

                    // Don't load initial data - wait for strategy selection
                    // professionalChart.loadHistoricalData();

                    console.log('Professional TradingView chart initialized for Strategy Builder');
                } else {
                    console.log('Waiting for TradingView library...');
                    setTimeout(initProfessionalChart, 100);
                }
            };

            // Wait for DOM to be fully ready
            setTimeout(initProfessionalChart, 500);

            // Sidebar tab functionality
            const sidebarTabs = document.querySelectorAll('.sidebar-tab');
            const sidebarPanels = document.querySelectorAll('.sidebar-panel');

            sidebarTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const targetTab = tab.dataset.tab;

                    // Remove active class from all tabs and panels
                    sidebarTabs.forEach(t => t.classList.remove('active'));
                    sidebarPanels.forEach(p => p.classList.remove('active'));

                    // Add active class to clicked tab and corresponding panel
                    tab.classList.add('active');
                    document.getElementById(`${targetTab}-panel`).classList.add('active');

                    // Handle marking tools activation/deactivation
                    if (targetTab === 'marking') {
                        console.log('Switching to marking tools');
                        console.log('window.markingTools available:', !!window.markingTools);

                        // Enable marking mode
                        if (window.markingTools) {
                            window.markingTools.enableMarkingMode();
                        } else {
                            console.warn('Marking tools not initialized yet');
                            // Try to initialize marking tools if not available
                            const chartInstance = window.professionalChart || window.tradingViewChart;
                            if (chartInstance && typeof MarkingTools !== 'undefined') {
                                console.log('Attempting to initialize marking tools...');
                                window.markingTools = new MarkingTools(chartInstance);
                                window.markingTools.enableMarkingMode();
                            } else {
                                console.error('Chart or MarkingTools class not available');
                                console.log('MarkingTools available:', typeof MarkingTools !== 'undefined');
                                console.log('professionalChart available:', !!window.professionalChart);
                                console.log('tradingViewChart available:', !!window.tradingViewChart);
                            }
                        }
                    } else {
                        // Disable marking mode when switching away from marking tools
                        if (window.markingTools) {
                            window.markingTools.disableMarkingMode();
                        }
                    }
                });
            });

            // Header symbol change functionality
            document.getElementById('main-change-symbol').addEventListener('click', () => {
                const newSymbol = document.getElementById('main-symbol-input').value.trim().toUpperCase();
                if (newSymbol && professionalChart) {
                    professionalChart.changeSymbol(newSymbol);
                    document.getElementById('header-symbol').textContent = newSymbol;

                    // Update sidebar symbol input
                    document.getElementById('symbol').value = newSymbol;
                }
            });

            // Header timeframe buttons
            document.querySelectorAll('.timeframe-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    const interval = btn.dataset.interval;
                    if (professionalChart) {
                        professionalChart.changeInterval(interval);

                        // Update sidebar timeframe select
                        document.getElementById('timeframe').value = interval;
                    }
                });
            });

            // Sync sidebar controls with header
            document.getElementById('symbol').addEventListener('change', (e) => {
                document.getElementById('main-symbol-input').value = e.target.value;
            });

            document.getElementById('timeframe').addEventListener('change', (e) => {
                const interval = e.target.value;
                // Find and click the corresponding timeframe button
                const btn = document.querySelector(`[data-interval="${interval}"]`);
                if (btn) {
                    btn.click();
                }
            });



            // Connection status simulation (replace with real WebSocket status)
            setTimeout(() => {
                const dot = document.getElementById('main-connection-dot');
                const text = document.getElementById('main-connection-text');
                dot.classList.add('connected');
                text.textContent = 'Live Data';
            }, 3000);

            // Update header price display (replace with real data)
            const updateHeaderPrice = () => {
                const symbol = document.getElementById('header-symbol').textContent;
                if (symbol.includes('BTC')) {
                    const price = 45000 + (Math.random() - 0.5) * 1000;
                    const change = (Math.random() - 0.5) * 2;
                    document.getElementById('header-price').textContent = `$${price.toFixed(2)}`;
                    document.getElementById('header-change').textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                    document.getElementById('header-change').className = `price-change ${change >= 0 ? 'positive' : 'negative'}`;
                }
            };

            setInterval(updateHeaderPrice, 5000);
            updateHeaderPrice();

            // Initialize multi-indicator configuration manager
            if (window.MultiIndicatorConfigManager) {
                window.multiIndicatorConfig = new MultiIndicatorConfigManager();
                console.log('Multi-Indicator Configuration Manager initialized');
            }

            // Initialize legacy components for backward compatibility
            if (window.IndicatorsManager) {
                window.indicatorsManager = new IndicatorsManager();
            }
            // TradeManager disabled - using new MarkingTools instead
            // if (window.TradeManager) {
            //     window.tradeManager = new TradeManager();
            // }
        });
    </script>
</body>
</html>
