<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Futures Trading Chart UI</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js"></script>
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
  <link rel="stylesheet" href="./futures_chart_theme_1.css">
  <style>
    body { font-family: 'Inter', sans-serif !important; }
    .chart-placeholder {
      background: linear-gradient(135deg, #181A20 60%, #23262F 100%);
      border-radius: var(--radius-md);
      min-height: 320px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--muted-foreground);
      font-size: 1.5rem;
      font-weight: 600;
      letter-spacing: 0.05em;
    }
    .volume-placeholder {
      background: #22252B;
      border-radius: var(--radius-sm);
      min-height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--muted-foreground);
      font-size: 1rem;
      font-weight: 500;
    }
    .panel {
      background: var(--card);
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border);
      padding: 1rem;
      min-height: 180px;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    .buy-btn {
      background: var(--secondary);
      color: var(--secondary-foreground);
      font-weight: 600;
      border-radius: var(--radius-sm);
      padding: 0.5rem 1.25rem;
      transition: transform 0.15s, box-shadow 0.15s;
    }
    .buy-btn:hover { transform: scale(1.05); box-shadow: var(--shadow); }
    .sell-btn {
      background: var(--primary);
      color: var(--primary-foreground);
      font-weight: 600;
      border-radius: var(--radius-sm);
      padding: 0.5rem 1.25rem;
      transition: transform 0.15s, box-shadow 0.15s;
    }
    .sell-btn:hover { transform: scale(1.05); box-shadow: var(--shadow); }
    .toolbar-btn {
      background: var(--muted);
      color: var(--foreground);
      border-radius: var(--radius-sm);
      padding: 0.4rem 1rem;
      font-weight: 500;
      margin-right: 0.5rem;
      transition: background 0.15s;
    }
    .toolbar-btn:last-child { margin-right: 0; }
    .toolbar-btn:hover { background: var(--card); }
    .input, select {
      background: var(--input);
      color: var(--foreground);
      border: 1px solid var(--border);
      border-radius: var(--radius-sm);
      padding: 0.4rem 0.75rem;
      font-size: 1rem;
      outline: none;
      transition: border 0.15s;
    }
    .input:focus, select:focus { border-color: var(--ring); }
    @media (max-width: 1024px) {
      .main-grid { grid-template-columns: 1fr !important; }
      .side-panels { flex-direction: column !important; gap: 1rem !important; }
    }
  </style>
</head>
<body class="min-h-screen w-full bg-[var(--background)] text-[var(--foreground)]">
  <!-- Toolbar/Header -->
  <header class="w-full px-4 py-3 flex flex-wrap items-center gap-2 border-b border-[var(--border)] bg-[var(--card)]">
    <select class="toolbar-btn" aria-label="Symbol">
      <option>BTC/USDT</option>
      <option>ETH/USDT</option>
      <option>BNB/USDT</option>
    </select>
    <select class="toolbar-btn" aria-label="Timeframe">
      <option>1m</option>
      <option>5m</option>
      <option>15m</option>
      <option>1h</option>
      <option>4h</option>
      <option>1d</option>
    </select>
    <button class="toolbar-btn flex items-center gap-1">
      <i data-lucide="activity" class="w-4 h-4"></i>
      Indicators
    </button>
    <div class="flex-1"></div>
    <button class="buy-btn">Buy</button>
    <button class="sell-btn ml-2">Sell</button>
  </header>

  <!-- Main Chart Area -->
  <main class="w-full px-4 py-4">
    <div class="main-grid grid grid-cols-1 lg:grid-cols-1 gap-4">
      <div>
        <div class="chart-placeholder mb-2">
          Candlestick Chart (Placeholder)
        </div>
        <div class="volume-placeholder">
          Volume Bars (Placeholder)
        </div>
      </div>
    </div>
    <!-- Panels: Order Book, Order Entry, Positions -->
    <div class="side-panels flex flex-row gap-4 mt-6">
      <!-- Order Book Panel -->
      <div class="panel flex-1 min-w-[220px]">
        <div class="font-semibold text-lg mb-1">Order Book</div>
        <div class="flex justify-between text-xs text-[var(--muted-foreground)]">
          <span>Price (USDT)</span><span>Amount</span>
        </div>
        <div class="flex flex-col gap-1 mt-2">
          <div class="flex justify-between text-red-400"><span>42,100.5</span><span>0.25</span></div>
          <div class="flex justify-between text-red-400"><span>42,099.0</span><span>0.18</span></div>
          <div class="flex justify-between text-green-400"><span>42,098.5</span><span>0.32</span></div>
          <div class="flex justify-between text-green-400"><span>42,097.0</span><span>0.40</span></div>
        </div>
      </div>
      <!-- Order Entry Panel -->
      <div class="panel flex-1 min-w-[260px]">
        <div class="font-semibold text-lg mb-1">Order Entry</div>
        <div class="flex gap-2 mb-2">
          <button class="buy-btn flex-1">Buy</button>
          <button class="sell-btn flex-1">Sell</button>
        </div>
        <div class="flex flex-col gap-2">
          <select class="input" aria-label="Order Type">
            <option>Market</option>
            <option>Limit</option>
            <option>Stop</option>
          </select>
          <input class="input" type="number" placeholder="Price (USDT)" />
          <input class="input" type="number" placeholder="Quantity" />
          <select class="input" aria-label="Leverage">
            <option>1x</option>
            <option>5x</option>
            <option>10x</option>
            <option>20x</option>
          </select>
          <button class="buy-btn mt-2">Place Order</button>
        </div>
      </div>
      <!-- Positions Panel -->
      <div class="panel flex-1 min-w-[220px]">
        <div class="font-semibold text-lg mb-1">Positions</div>
        <div class="flex justify-between text-xs text-[var(--muted-foreground)]">
          <span>Symbol</span><span>Size</span><span>P&L</span>
        </div>
        <div class="flex flex-col gap-1 mt-2">
          <div class="flex justify-between">
            <span>BTC/USDT</span>
            <span>0.15</span>
            <span class="text-green-400">+$120</span>
          </div>
          <div class="flex justify-between">
            <span>ETH/USDT</span>
            <span>0.80</span>
            <span class="text-red-400">-$45</span>
          </div>
        </div>
      </div>
    </div>
  </main>
  <script>lucide.createIcons();</script>
</body>
</html>
