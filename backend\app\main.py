"""
Strategy Builder - Main FastAPI Application
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import os
import logging
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

from app.core.config import settings
from app.core.database import init_db, test_connection
from app.api import ohlcv
from app.api import indicators_simple as indicators
from app.api import trades_simple as trades
from app.api import websocket
from app.api import strategies
from app.api import marks

# Create FastAPI app
app = FastAPI(
    title="Strategy Builder API",
    description="Trading Strategy Builder with Chart Analysis",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
frontend_path = Path(__file__).parent.parent.parent / "frontend"
if frontend_path.exists():
    app.mount("/static", StaticFiles(directory=str(frontend_path / "static")), name="static")

# Include API routers
app.include_router(ohlcv.router, prefix="/api/v1/ohlcv", tags=["OHLCV Data"])
app.include_router(indicators.router, prefix="/api/v1/indicators", tags=["Technical Indicators"])
app.include_router(trades.router, prefix="/api/v1/trades", tags=["Trade Management"])
app.include_router(websocket.router, prefix="/api/v1/ws", tags=["WebSocket Streams"])
app.include_router(strategies.router, prefix="/api/v1/strategies", tags=["Strategy Management"])
app.include_router(marks.router, prefix="/api/v1/marks", tags=["Manual Marks"])

@app.on_event("startup")
async def startup_event():
    """Initialize database and services on startup"""
    try:
        # Test connection first
        if await test_connection():
            await init_db()
            logger.info("Database initialized successfully")
        else:
            logger.warning("Database connection failed, but starting server anyway")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        # Don't exit, let the app start anyway

    # Initialize enhanced services
    try:
        from app.services.indicator_streaming import StreamingManager
        await StreamingManager.start_streaming()
        logger.info("Indicator streaming service started")
    except Exception as e:
        logger.error(f"Failed to start streaming service: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup services on shutdown"""
    try:
        from app.services.indicator_streaming import StreamingManager
        await StreamingManager.stop_streaming()
        logger.info("Indicator streaming service stopped")
    except Exception as e:
        logger.error(f"Error stopping streaming service: {e}")

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main frontend page"""
    frontend_file = frontend_path / "templates" / "index.html"
    if frontend_file.exists():
        try:
            return HTMLResponse(content=frontend_file.read_text(encoding='utf-8'), status_code=200)
        except UnicodeDecodeError:
            # Fallback to reading with different encoding
            return HTMLResponse(content=frontend_file.read_text(encoding='utf-8', errors='ignore'), status_code=200)
    return HTMLResponse(content="<h1>Strategy Builder API</h1><p>Frontend not found. Visit <a href='/api/docs'>/api/docs</a> for API documentation.</p>")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "version": "1.0.0"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
